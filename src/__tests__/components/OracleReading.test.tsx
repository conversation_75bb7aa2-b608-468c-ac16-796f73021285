import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { OracleReading } from '../../components/OracleReading';
import { fallingBirdTheme } from '../../theme/muiTheme';

// Mock the card utilities
jest.mock('../../utils/cardUtils', () => ({
  drawCards: jest.fn((deck, count) => {
    return Array.from({ length: count }, (_, i) => ({
      id: `card-${i}`,
      title: `Test Card ${i + 1}`,
      description: `Description ${i + 1}`,
      keywords: [`keyword${i + 1}`],
      image: `image${i + 1}.jpg`,
      isRevealed: false,
      isReversed: false,
      position: i,
    }));
  }),
  getCardCount: jest.fn((spreadType) => {
    const counts = { 'single': 1, 'three': 3 };
    return counts[spreadType] || 1;
  }),
  getSpreadPositions: jest.fn((spreadType) => {
    const positions = {
      'single': [{ x: 0, y: 0, label: 'Your Card' }],
      'three': [
        { x: -120, y: 0, label: 'Past' },
        { x: 0, y: 0, label: 'Present' },
        { x: 120, y: 0, label: 'Future' }
      ]
    };
    return positions[spreadType] || positions['single'];
  }),
  sleep: jest.fn(() => Promise.resolve()),
}));

// Mock the components
jest.mock('../../components/Deck', () => ({
  Deck: ({ onCardDraw }: { onCardDraw: () => void }) => (
    <div data-testid="deck" onClick={onCardDraw}>
      Deck Component
    </div>
  ),
}));

jest.mock('../../components/Card', () => ({
  Card: ({ card, onReveal }: any) => (
    <div 
      data-testid={`card-${card.id}`}
      onClick={() => onReveal(card.id)}
    >
      {card.isRevealed ? card.title : 'Card Back'}
    </div>
  ),
}));

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('OracleReading Component', () => {
  test('renders question phase initially', () => {
    renderWithTheme(<OracleReading />);

    expect(screen.getByText('Ask Your Question')).toBeInTheDocument();
    expect(screen.getByText("I'm Ready")).toBeInTheDocument();
    expect(screen.getByText('Hold your question in your mind and heart...')).toBeInTheDocument();
  });

  test('transitions through phases correctly', async () => {
    renderWithTheme(<OracleReading />);

    // Start in question phase
    expect(screen.getByText('Ask Your Question')).toBeInTheDocument();

    // Click "I'm Ready" to go to shuffle phase
    const readyButton = screen.getByText("I'm Ready");
    fireEvent.click(readyButton);

    await waitFor(() => {
      expect(screen.getByText('Shuffle Deck')).toBeInTheDocument();
    });

    // Click "Shuffle Deck" to go to selection phase
    const shuffleButton = screen.getByText('Shuffle Deck');
    fireEvent.click(shuffleButton);

    await waitFor(() => {
      expect(screen.getByText('Choose Your Spread')).toBeInTheDocument();
      expect(screen.getByText('Single Card')).toBeInTheDocument();
      expect(screen.getByText('Three Card Spread')).toBeInTheDocument();
    });
  });

  test('shows deck in drawing phase', async () => {
    renderWithTheme(<OracleReading />);

    // Progress through phases: question -> shuffle -> selection
    const readyButton = screen.getByText("I'm Ready");
    fireEvent.click(readyButton);

    await waitFor(() => {
      const shuffleButton = screen.getByText('Shuffle Deck');
      fireEvent.click(shuffleButton);
    });

    await waitFor(() => {
      const threeCardButton = screen.getByText('Three Card Spread');
      fireEvent.click(threeCardButton);
    });

    await waitFor(() => {
      expect(screen.getByText('Drawing Your Cards...')).toBeInTheDocument();
    });
  });

  test('draws cards when deck is clicked', async () => {
    renderWithTheme(<OracleReading />);

    // Progress through phases: question -> shuffle -> selection
    const readyButton = screen.getByText("I'm Ready");
    fireEvent.click(readyButton);

    await waitFor(() => {
      const shuffleButton = screen.getByText('Shuffle Deck');
      fireEvent.click(shuffleButton);
    });

    await waitFor(() => {
      const singleCardButton = screen.getByText('Single Card');
      fireEvent.click(singleCardButton);
    });

    // Wait for cards to be drawn and then reading phase
    await waitFor(() => {
      expect(screen.getByText('Your Reading')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  test('reveals cards when clicked', async () => {
    renderWithTheme(<OracleReading />);

    // Progress through phases: question -> shuffle -> selection
    const readyButton = screen.getByText("I'm Ready");
    fireEvent.click(readyButton);

    await waitFor(() => {
      const shuffleButton = screen.getByText('Shuffle Deck');
      fireEvent.click(shuffleButton);
    });

    await waitFor(() => {
      const singleCardButton = screen.getByText('Single Card');
      fireEvent.click(singleCardButton);
    });

    // Wait for reading phase and click on card to reveal
    await waitFor(() => {
      expect(screen.getByText('Your Reading')).toBeInTheDocument();
    }, { timeout: 3000 });

    await waitFor(() => {
      const card = screen.getByTestId('card-card-0');
      fireEvent.click(card);
    });

    await waitFor(() => {
      expect(screen.getByText('Test Card 1')).toBeInTheDocument();
    });
  });

  test('shows completion message when all cards are revealed', async () => {
    renderWithTheme(<OracleReading />);

    // Progress through phases: question -> shuffle -> selection
    const readyButton = screen.getByText("I'm Ready");
    fireEvent.click(readyButton);

    await waitFor(() => {
      const shuffleButton = screen.getByText('Shuffle Deck');
      fireEvent.click(shuffleButton);
    });

    await waitFor(() => {
      const singleCardButton = screen.getByText('Single Card');
      fireEvent.click(singleCardButton);
    });

    // Wait for reading phase
    await waitFor(() => {
      expect(screen.getByText('Your Reading')).toBeInTheDocument();
    }, { timeout: 3000 });

    // Reveal the card
    await waitFor(() => {
      const card = screen.getByTestId('card-card-0');
      fireEvent.click(card);
    });

    // Check for completion
    await waitFor(() => {
      expect(screen.getByText('Your reading is complete. Take time to reflect on the messages.')).toBeInTheDocument();
      expect(screen.getByText('New Reading')).toBeInTheDocument();
    });
  });

  test('resets reading when New Reading button is clicked', async () => {
    renderWithTheme(<OracleReading />);

    // Progress through phases: question -> shuffle -> selection
    const readyButton = screen.getByText("I'm Ready");
    fireEvent.click(readyButton);

    await waitFor(() => {
      const shuffleButton = screen.getByText('Shuffle Deck');
      fireEvent.click(shuffleButton);
    });

    await waitFor(() => {
      const singleCardButton = screen.getByText('Single Card');
      fireEvent.click(singleCardButton);
    });

    // Wait for reading phase
    await waitFor(() => {
      expect(screen.getByText('Your Reading')).toBeInTheDocument();
    }, { timeout: 3000 });

    await waitFor(() => {
      const card = screen.getByTestId('card-card-0');
      fireEvent.click(card);
    });

    // Click New Reading
    await waitFor(() => {
      const newReadingButton = screen.getByText('New Reading');
      fireEvent.click(newReadingButton);
    });

    // Should be back to question phase
    await waitFor(() => {
      expect(screen.getByText('Ask Your Question')).toBeInTheDocument();
    });
  });

  test('handles three-card spread correctly', async () => {
    renderWithTheme(<OracleReading />);

    // Progress through phases: question -> shuffle -> selection
    const readyButton = screen.getByText("I'm Ready");
    fireEvent.click(readyButton);

    await waitFor(() => {
      const shuffleButton = screen.getByText('Shuffle Deck');
      fireEvent.click(shuffleButton);
    });

    await waitFor(() => {
      const threeCardButton = screen.getByText('Three Card Spread');
      fireEvent.click(threeCardButton);
    });

    // Wait for reading phase
    await waitFor(() => {
      expect(screen.getByText('Your Reading')).toBeInTheDocument();
    }, { timeout: 3000 });

    await waitFor(() => {
      expect(screen.getByTestId('card-card-0')).toBeInTheDocument();
      expect(screen.getByTestId('card-card-1')).toBeInTheDocument();
      expect(screen.getByTestId('card-card-2')).toBeInTheDocument();
    });
  });
});

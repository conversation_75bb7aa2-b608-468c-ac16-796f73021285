import '@testing-library/jest-dom';
import React from 'react';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => {
  const createMotionComponent = (component: any) => {
    const MockedComponent = React.forwardRef((props: any, ref: any) => {
      // Filter out framer-motion specific props to avoid React warnings
      const {
        animate,
        initial,
        exit,
        transition,
        whileHover,
        whileTap,
        whileInView,
        whileFocus,
        whileDrag,
        drag,
        dragConstraints,
        dragElastic,
        dragMomentum,
        dragTransition,
        layout,
        layoutId,
        ...filteredProps
      } = props;

      // If component is a string, create a DOM element
      if (typeof component === 'string') {
        return React.createElement(component, { ...filteredProps, ref });
      }

      // If component is a React component, render it directly
      return React.createElement(component, { ...filteredProps, ref });
    });
    MockedComponent.displayName = `Motion${typeof component === 'string' ? component : component.displayName || component.name || 'Component'}`;
    return MockedComponent;
  };

  // Create a motion function that can be called with any component
  const motionFunction = (component: any) => createMotionComponent(component);

  // Add common HTML elements as properties
  motionFunction.div = createMotionComponent('div');
  motionFunction.span = createMotionComponent('span');
  motionFunction.p = createMotionComponent('p');
  motionFunction.h1 = createMotionComponent('h1');
  motionFunction.h2 = createMotionComponent('h2');
  motionFunction.h3 = createMotionComponent('h3');
  motionFunction.h4 = createMotionComponent('h4');
  motionFunction.h5 = createMotionComponent('h5');
  motionFunction.h6 = createMotionComponent('h6');
  motionFunction.button = createMotionComponent('button');
  motionFunction.a = createMotionComponent('a');
  motionFunction.img = createMotionComponent('img');
  motionFunction.section = createMotionComponent('section');
  motionFunction.article = createMotionComponent('article');
  motionFunction.nav = createMotionComponent('nav');
  motionFunction.header = createMotionComponent('header');
  motionFunction.footer = createMotionComponent('footer');
  motionFunction.main = createMotionComponent('main');
  motionFunction.aside = createMotionComponent('aside');
  motionFunction.ul = createMotionComponent('ul');
  motionFunction.ol = createMotionComponent('ol');
  motionFunction.li = createMotionComponent('li');

  return {
    motion: motionFunction,
    AnimatePresence: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));
